const UserProfileService = require('./userProfileService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for user Basic Profile.
 */
class UserProfileController {

    /**
     * @desc This function is being used to get user details
     * <AUTHOR>
     * @since 22/09/2023
     * @param {Object} req Request
     * @param {Object} req.body RequestBody
     * @param {function} res Response
     */
    // static async getUserDetails (req, res) {
    //     try {
    //         const data = await UserProfileService.getUserDetails(req, res.locals.user);
    //         Utils.sendResponse(null, data, res, res.__('SUCCESS'));
    //     } catch (error) {
    //         Utils.sendResponse(error, null, res, error.message);
    //     }
    // }

    /**
     * @desc This function is being used to export csv files
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async exportToCSV (req, res) {
        return await UserProfileService.exportToCSV(req, res);
    }

    /**
     * @desc This function is being used to import data from csv files
     * @param {Object} req Request
     * @param {function} res Response
     */
    static async importFromCSV (req, res) {
        try {
            const data = await UserProfileService.importFromCSV(req, res);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

      /**
   * @desc This function is being used to get details of all users
   * @param {Object} req Request
   * @param {function} res Response
   */
  // static async getAllUserDetails(req, res) {
  //   try {
  //     const data = await UserProfileService.getAllUserDetails(req);
  //     Utils.sendResponse(null, data, res, res.__('SUCCESS'));
  //   } catch (error) {
  //     Utils.sendResponse(error, null, res, error.message);
  //   }
  // }
}

module.exports = UserProfileController;
