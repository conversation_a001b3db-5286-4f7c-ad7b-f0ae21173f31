/**
 * Main Socket Controller
 * Coordinates all socket modules and provides a unified interface
 */

// Import all managers and utilities
const SocketEventHandlers = require('./managers/socketEventHandlers');
const UserManager = require('./managers/userManager');
const AuctionManager = require('./managers/auctionManager');
const BiddingManager = require('./managers/biddingManager');
const PlayerManager = require('./managers/playerManager');
const CommunicationManager = require('./managers/communicationManager');
const SystemManager = require('./managers/systemManager');
const SocketUtils = require('./utils/socketUtils');
const auctionState = require('./state/auctionState');
const CONSOLE_LOGGER = require('../../util/logger');

/**
 * Main Socket Controller Class
 * Provides a unified interface for all socket operations
 */
class SocketController {
  /**
   * Initialize socket connection and set up event listeners
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   */
  static initializeSocket(io, socket) {
    // Set unique socket ID
    socket.id = SocketUtils.getUniqueID();
    console.log('Socket.IO client connected with ID:', socket.id);

    // Set initial connection state
    socket.isAlive = true;
    socket.isDisconnected = false;

    // Set up all event listeners
    SocketController.setupEventListeners(io, socket);

    // Log connection
    CONSOLE_LOGGER.info(`Socket initialized: ${socket.id}`);
  }

  /**
   * Set up all socket event listeners
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   */
  static setupEventListeners(io, socket) {
    // User management events
    socket.on('registerUser', async (messageBody) => {
      await SocketEventHandlers.handleRegisterUser(io, socket, messageBody);
    });

    socket.on('disconnectUser', async (messageBody) => {
      await SocketEventHandlers.handleDisconnectUser(io, socket, messageBody);
    });

    socket.on('getUserDetails', async (messageBody) => {
      await SocketEventHandlers.handleGetUserDetails(io, socket, messageBody);
    });

    socket.on('getAllUserDetails', async (messageBody) => {
      await SocketEventHandlers.handleGetAllUserDetails(io, socket, messageBody);
    });

    // Auction management events
    socket.on('startAuction', async (messageBody) => {
      await SocketEventHandlers.handleStartAuction(io, socket, messageBody);
    });

    socket.on('getNewPlayer', async (data) => {
      await SocketEventHandlers.handleGetNewPlayer(io, socket, data);
    });

    socket.on('lastCall', async (messageBody) => {
      await SocketEventHandlers.handleLastCall(io, socket, messageBody);
    });

    socket.on('auctionCompleted', async (data) => {
      await SocketEventHandlers.handleAuctionCompleted(io, socket, data);
    });

    socket.on('getUpcomingPlayers', async (messageBody) => {
      await SocketEventHandlers.handleGetUpcomingPlayers(io, socket, messageBody);
    });

    // Bidding events
    socket.on('addBid', async (messageBody) => {
      await SocketEventHandlers.handleAddBid(io, socket, messageBody);
    });

    // System events
    socket.on('reconnect', async (messageBody) => {
      await SocketEventHandlers.handleReconnect(io, socket, messageBody);
    });

    socket.on('auctionStatus', async (messageBody) => {
      await SocketEventHandlers.handleAuctionStatus(io, socket, messageBody);
    });

    socket.on('systemHealth', async (messageBody) => {
      await SocketEventHandlers.handleSystemHealth(socket, messageBody);
    });

    socket.on('resetAuction', async (messageBody) => {
      await SocketEventHandlers.handleResetAuction(io, socket, messageBody);
    });

    // Connection events
    socket.on('disconnect', async (reason) => {
      await SocketEventHandlers.handleDisconnect(io, socket, reason);
    });

    socket.on('error', (error) => {
      SocketEventHandlers.handleError(socket, error);
    });
  }

  // Expose individual managers for direct access if needed
  static get UserManager() {
    return UserManager;
  }

  static get AuctionManager() {
    return AuctionManager;
  }

  static get BiddingManager() {
    return BiddingManager;
  }

  static get PlayerManager() {
    return PlayerManager;
  }

  static get CommunicationManager() {
    return CommunicationManager;
  }

  static get SystemManager() {
    return SystemManager;
  }

  static get SocketUtils() {
    return SocketUtils;
  }

  static get AuctionState() {
    return auctionState;
  }

  // Legacy compatibility methods - these delegate to the appropriate managers
  
  /**
   * @deprecated Use UserManager.validateUser instead
   */
  static async validateUser(payload, checkAdmin = false) {
    return await UserManager.validateUser(payload, checkAdmin);
  }

  /**
   * @deprecated Use UserManager.registerUser instead
   */
  static async registerUser(io, socket, payload) {
    return await UserManager.registerUser(io, socket, payload);
  }

  /**
   * @deprecated Use UserManager.disconnectClient instead
   */
  static async disconnectClient(io, socket) {
    return await UserManager.disconnectClient(io, socket);
  }

  /**
   * @deprecated Use AuctionManager.startAuction instead
   */
  static async startAuction(io, socket, payload) {
    return await AuctionManager.startAuction(io, socket, payload);
  }

  /**
   * @deprecated Use AuctionManager.getAnotherPlayer instead
   */
  static async getAnotherPlayer(io, socket, payload) {
    return await AuctionManager.getAnotherPlayer(io, socket, payload);
  }

  /**
   * @deprecated Use BiddingManager.addBid instead
   */
  static async addBid(io, socket, payload) {
    return await BiddingManager.addBid(io, socket, payload);
  }

  /**
   * @deprecated Use PlayerManager.getPlayer instead
   */
  static async getPlayer(io, socket, isRecursive = false) {
    return await PlayerManager.getPlayer(io, socket, isRecursive);
  }

  /**
   * @deprecated Use CommunicationManager.broadcastMessage instead
   */
  static async broadcastMessage(io, action, text, successEvent = null, additionalValues = null) {
    return await CommunicationManager.broadcastMessage(io, action, text, successEvent, additionalValues);
  }

  /**
   * @deprecated Use CommunicationManager.sendResponseToClient instead
   */
  static sendResponseToClient(socket, data, event) {
    return CommunicationManager.sendResponseToClient(socket, data, event);
  }

  /**
   * @deprecated Use SystemManager.auctionStatus instead
   */
  static async auctionStatus(io, socket, payload) {
    return await SystemManager.auctionStatus(io, socket, payload);
  }

  /**
   * @deprecated Use SystemManager.reConnect instead
   */
  static async reConnect(io, socket, payload) {
    return await SystemManager.reConnect(io, socket, payload);
  }

  /**
   * @deprecated Use SystemManager.auctionCompleted instead
   */
  static async auctionCompleted(io, socket, payload) {
    return await SystemManager.auctionCompleted(io, socket, payload);
  }

  /**
   * @deprecated Use AuctionManager.lastCall instead
   */
  static async lastCall(io, socket, payload) {
    return await AuctionManager.lastCall(io, socket, payload);
  }

  /**
   * @deprecated Use UserManager.getUserDetails instead
   */
  static async getUserDetails(socket) {
    return await UserManager.getUserDetails(socket);
  }

  /**
   * @deprecated Use UserManager.getAllUserDetails instead
   */
  static async getAllUserDetails(io, socket, payload) {
    return await UserManager.getAllUserDetails(io, socket, payload);
  }

  /**
   * @deprecated Use AuctionManager.handleUpcomingPlayersRequest instead
   */
  static async handleUpcomingPlayersRequest(io, socket) {
    return await AuctionManager.handleUpcomingPlayersRequest(io, socket);
  }

  /**
   * @deprecated Use SocketUtils.getPlayersConfigResponse instead
   */
  static getPlayersConfigResponse() {
    const cycleStatus = auctionState.getPlayerCycleStatus();
    const biddingConfig = auctionState.getBiddingConfig();
    return SocketUtils.getPlayersConfigResponse(
      cycleStatus.playerTypesCycleArr,
      biddingConfig.maxBidConfig,
      biddingConfig.averagePlayerCost
    );
  }

  /**
   * Get current auction state snapshot for debugging
   */
  static getStateSnapshot() {
    return auctionState.getStateSnapshot();
  }

  /**
   * Reset entire auction system (admin only)
   */
  static async resetSystem() {
    auctionState.reset();
    await AuctionManager.clearOlderData();
    CONSOLE_LOGGER.info('Socket system reset completed');
  }
}

module.exports = SocketController;
