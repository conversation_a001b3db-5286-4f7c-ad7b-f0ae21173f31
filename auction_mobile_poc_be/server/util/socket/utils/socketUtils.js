const Constants = require('../../../util/constants');
const CONSOLE_LOGGER = require('../../../util/logger');

/**
 * Socket utility functions
 * Contains helper functions used across the socket system
 */
class SocketUtils {
  /**
   * Convert Sequelize model to plain object
   * @param {Object} model - Sequelize model instance
   * @returns {Object|null} Plain object or null
   */
  static modelToPlainObject(model) {
    if (!model) return null;

    try {
      // If it's already a plain object, return it
      if (!model.dataValues) return model;

      // Convert to plain object
      const plainObj = model.toJSON ? model.toJSON() : { ...model.dataValues };

      // Handle associations
      Object.keys(plainObj).forEach((key) => {
        if (plainObj[key] && typeof plainObj[key] === 'object') {
          if (Array.isArray(plainObj[key])) {
            plainObj[key] = plainObj[key].map((item) => 
              (item && item.dataValues ? SocketUtils.modelToPlainObject(item) : item)
            );
          } else if (plainObj[key].dataValues) {
            plainObj[key] = SocketUtils.modelToPlainObject(plainObj[key]);
          }
        }
      });

      return plainObj;
    } catch (error) {
      console.error('Error converting model to plain object:', error);
      return model;
    }
  }

  /**
   * Get players configuration response
   * @param {Array} playerTypesCycleArr - Array of player types in cycle
   * @param {Object} maxBidConfig - Max bid configuration
   * @param {number} averagePlayerCost - Average player cost
   * @returns {Object} Players configuration object
   */
  static getPlayersConfigResponse(playerTypesCycleArr, maxBidConfig, averagePlayerCost) {
    let batterCount = 0;
    let wicketKeeperCount = 0;
    let bowlerCount = 0;

    playerTypesCycleArr.forEach((item) => {
      if (item === Constants.PLAYER_TYPES.BAT) batterCount += 1;
      else if (item === Constants.PLAYER_TYPES.BALL) bowlerCount += 1;
      else wicketKeeperCount += 1;
    });

    CONSOLE_LOGGER.info('getPlayersConfigResponse ~ playerTypesCycleArr:', playerTypesCycleArr);

    return {
      averagePlayerCost,
      batter: batterCount,
      wicketKeeper: wicketKeeperCount,
      bowler: bowlerCount,
      batterMax: maxBidConfig.BAT,
      wicketKeeperMax: maxBidConfig.WK,
      bowlerMax: maxBidConfig.BALL
    };
  }

  /**
   * Set player types cycle array
   * @param {number} battersCount - Number of batters
   * @param {number} bowlersCount - Number of bowlers  
   * @param {number} wicketsCount - Number of wicket keepers
   * @returns {Array} Player types cycle array
   */
  static setPlayerTypesCycle(battersCount = 1, bowlersCount = 1, wicketsCount = 1) {
    console.log('🚀🚀🚀 ~ SocketUtils ~ setPlayerTypesCycle ~ called with:', { 
      battersCount, bowlersCount, wicketsCount 
    });

    // Always maintain the order: batsmen first, then bowlers, then wicket keepers
    // Create arrays for each type
    const batsmen = Array(battersCount).fill(Constants.PLAYER_TYPES.BAT);
    const bowlers = Array(bowlersCount).fill(Constants.PLAYER_TYPES.BALL);
    const wicketKeepers = Array(wicketsCount).fill(Constants.PLAYER_TYPES.WK);

    // Combine them in the correct order
    const playerTypesCycleArr = [...batsmen, ...bowlers, ...wicketKeepers];

    console.log('🚀🚀🚀 ~ SocketUtils ~ setPlayerTypesCycle ~ playerTypesCycleArr:', playerTypesCycleArr);
    
    return playerTypesCycleArr;
  }

  /**
   * Get player max bid configuration for a user
   * @param {string} userId - User ID
   * @param {Object} playersBidStatus - Players bid status object
   * @param {Object} maxBidConfig - Max bid configuration
   * @returns {Object} User's max bid configuration
   */
  static getPlayerMaxBidConfig(userId, playersBidStatus, maxBidConfig) {
    if (!playersBidStatus[userId]) {
      playersBidStatus[userId] = {
        [Constants.PLAYER_TYPES.BALL]: { max: maxBidConfig.BALL, current: 0 },
        [Constants.PLAYER_TYPES.BAT]: { max: maxBidConfig.BAT, current: 0 },
        [Constants.PLAYER_TYPES.WK]: { max: maxBidConfig.WK, current: 0 }
      };
    }

    return playersBidStatus[userId];
  }

  /**
   * Set max bid configuration
   * @param {Object} maxBidConfig - Max bid config object to update
   * @param {number} batMax - Max batters
   * @param {number} bowlerMax - Max bowlers
   * @param {number} wicketMax - Max wicket keepers
   */
  static setMaxBidConfig(maxBidConfig, batMax, bowlerMax, wicketMax) {
    maxBidConfig.BAT = batMax;
    maxBidConfig.BALL = bowlerMax;
    maxBidConfig.WK = wicketMax;
  }

  /**
   * Check if quota is exhausted for bidding
   * @param {Object} player - Player object with player_type
   * @param {Object} user - User object
   * @param {Object} playersBidStatus - Players bid status
   * @param {Object} maxBidConfig - Max bid configuration
   * @returns {boolean} True if quota exhausted
   */
  static isQuotaExhaustedForBidding(player, user, playersBidStatus, maxBidConfig) {
    const userMaxBidConfig = SocketUtils.getPlayerMaxBidConfig(user.id, playersBidStatus, maxBidConfig);
    CONSOLE_LOGGER.info('isQuotaExhaustedForBidding ~ userMaxBidConfig:', JSON.stringify(userMaxBidConfig, null, 2));
    
    if (!userMaxBidConfig) return false;

    return userMaxBidConfig[player.player_type.type].current === userMaxBidConfig[player.player_type.type].max;
  }

  /**
   * Generate unique ID for socket connections
   * @returns {string} Unique ID
   */
  static getUniqueID() {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * Calculate remaining time for auction
   * @param {number} auctionStartedTime - When auction started
   * @param {number} duration - Duration in seconds
   * @returns {number} Remaining time in seconds
   */
  static calculateRemainingTime(auctionStartedTime, duration) {
    const elapsedTime = Date.now() - auctionStartedTime;
    return Math.round(Math.max(0, duration * 1000 - elapsedTime) / 1000);
  }

  /**
   * Validate required parameters
   * @param {Object} params - Parameters to validate
   * @param {Array} required - Required parameter names
   * @throws {Error} If required parameters are missing
   */
  static validateRequiredParams(params, required) {
    const missing = required.filter(param => params[param] === undefined || params[param] === null);
    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  /**
   * Safe JSON parse with error handling
   * @param {string} jsonString - JSON string to parse
   * @param {*} defaultValue - Default value if parsing fails
   * @returns {*} Parsed object or default value
   */
  static safeJsonParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Error parsing JSON:', error);
      return defaultValue;
    }
  }

  /**
   * Deep clone object
   * @param {*} obj - Object to clone
   * @returns {*} Cloned object
   */
  static deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => SocketUtils.deepClone(item));
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = SocketUtils.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
    return obj;
  }
}

module.exports = SocketUtils;
