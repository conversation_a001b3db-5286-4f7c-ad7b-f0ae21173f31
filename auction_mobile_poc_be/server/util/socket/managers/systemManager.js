const _ = require('lodash');
const { Op } = require('sequelize');
const User = require('../../../models').user;
const auctionState = require('../state/auctionState');
const SocketUtils = require('../utils/socketUtils');
const CommunicationManager = require('./communicationManager');
const UserManager = require('./userManager');
const CONSOLE_LOGGER = require('../../../util/logger');

/**
 * System Manager
 * Handles system-level operations, status checks, and reconnection logic
 */
class SystemManager {
  /**
   * Handle user reconnection
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {Object} payload - Reconnection payload
   */
  static async reConnect(io, socket, payload) {
    try {
      const result = await UserManager.validateUser(payload);
      const isAdmin = result.user && result.user.role === 1;

      // Check if user was previously connected
      const existingSocketId = auctionState.findUserSocket(result.user.id);
      const wasDisconnected = existingSocketId && io.sockets.sockets.get(existingSocketId)?.isDisconnected;

      if (auctionState.getUserBySocket(socket.id)) {
        await CommunicationManager.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: payload.action,
            text: 'User already connected'
          },
          payload.action
        );
      } else {
        // Register the user with the new socket
        auctionState.addRegisteredUser(socket.id, result.user.id);

        // If this is an admin reconnecting and there was a previous socket marked as disconnected,
        // clean up the old socket entry
        if (isAdmin && wasDisconnected && existingSocketId) {
          console.log(`Admin (${result.user.id}) reconnecting. Cleaning up old socket: ${existingSocketId}`);
          auctionState.removeRegisteredUser(existingSocketId);
        }

        const tempStateObj = _.cloneDeep(auctionState.getPrevState());
        const auctionStatus = auctionState.getAuctionStatus();
        const remainingTime = SocketUtils.calculateRemainingTime(
          auctionStatus.auctionStartedTime,
          auctionStatus.duration
        );

        await CommunicationManager.sendResponseToClient(
          socket,
          {
            socketId: socket.id,
            status: true,
            action: payload.action,
            text: 'Reconnected successfully',
            tempStateObj: tempStateObj,
            ongoingAuctionFlag: remainingTime > 0,
            isAdmin: isAdmin
          },
          payload.action
        );

        // Send previous state if exists
        const prevState = auctionState.getPrevState();
        if (prevState && prevState.length > 0) {
          let findRemainingTime = remainingTime;
          if (prevState[0] !== 'startAuction') {
            if (prevState && prevState[1] && prevState[1].auctionRemainingTime) {
              findRemainingTime = prevState[1].auctionRemainingTime;
            }
          }

          await CommunicationManager.sendResponseToClient(
            socket,
            {
              socketId: socket.id,
              status: true,
              action: prevState[0],
              remainingTime: findRemainingTime,
              text: {
                ...prevState[1],
                remainingTime: findRemainingTime,
                amount: auctionStatus.initialAmount,
                timerDuration: auctionStatus.initialDuration
              }
            },
            prevState[0]
          );
        }

        await CommunicationManager.getAndFireLiveUsersEvent(io);
        await CommunicationManager.fireMinimumPlayerRequirement();
      }

      CONSOLE_LOGGER.info('reConnect completed for user:', result.user.id);
    } catch (error) {
      CONSOLE_LOGGER.error('reConnect error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || 'Reconnection failed'
        },
        'Error'
      );
    }
  }

  /**
   * Get current auction status
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {Object} payload - Status request payload
   */
  static async auctionStatus(io, socket, payload) {
    try {
      const result = await UserManager.validateUser(payload);
      const isAdmin = result.user && result.user.role === 1;

      const auctionStatus = auctionState.getAuctionStatus();
      const cycleStatus = auctionState.getPlayerCycleStatus();
      const biddingConfig = auctionState.getBiddingConfig();
      const registeredUsers = auctionState.getRegisteredUsers();

      const remainingTime = SocketUtils.calculateRemainingTime(
        auctionStatus.auctionStartedTime,
        auctionStatus.duration
      );

      const statusData = {
        isAuctionStarted: auctionStatus.isAuctionStarted,
        pauseActivated: auctionStatus.pauseActivated,
        isLastCallActive: auctionStatus.isLastCallActive,
        remainingTime: remainingTime,
        currentAuctionPlayerId: auctionStatus.currentAuctionPlayerId,
        connectedUsersCount: Object.keys(registeredUsers).length,
        playerCycleIndex: cycleStatus.playerTypesCycleIndex,
        totalPlayersInCycle: cycleStatus.playerTypesCycleArr.length,
        cycledPlayersCount: cycleStatus.playerIdsAlreadyCycled.length,
        ...SocketUtils.getPlayersConfigResponse(
          cycleStatus.playerTypesCycleArr,
          biddingConfig.maxBidConfig,
          biddingConfig.averagePlayerCost
        )
      };

      // Add admin-specific data
      if (isAdmin) {
        statusData.adminData = {
          registeredUserIds: Object.values(registeredUsers),
          auctionStateSnapshot: auctionState.getStateSnapshot()
        };
      }

      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: true,
          action: payload.action,
          data: statusData
        },
        payload.action
      );
    } catch (error) {
      CONSOLE_LOGGER.error('auctionStatus error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || 'Failed to get auction status'
        },
        'Error'
      );
    }
  }

  /**
   * Handle auction completion
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {Object} payload - Completion payload
   */
  static async auctionCompleted(io, socket, payload) {
    try {
      await UserManager.validateUser(payload);

      // Stop the auction
      auctionState.stopAuction();

      // Get final statistics
      const connectedUserIds = auctionState.getConnectedUserIds();
      const finalStats = await SystemManager._getFinalAuctionStats(connectedUserIds);

      await CommunicationManager.broadcastMessage(
        io,
        payload.action,
        {
          message: 'Auction completed successfully',
          finalStats,
          completedAt: new Date().toISOString()
        }
      );

      CONSOLE_LOGGER.info('Auction completed successfully');
    } catch (error) {
      CONSOLE_LOGGER.error('auctionCompleted error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: payload.action,
          text: error.message || 'Failed to complete auction'
        },
        'Error'
      );
    }
  }

  /**
   * Get final auction statistics
   * @private
   * @param {Array} userIds - Array of user IDs
   * @returns {Object} Final auction statistics
   */
  static async _getFinalAuctionStats(userIds) {
    try {
      if (userIds.length === 0) {
        return {
          totalUsers: 0,
          totalTransactions: 0,
          totalAmountSpent: 0,
          userStats: []
        };
      }

      const PlayerTransaction = require('../../../models').player_transaction;
      const Player = require('../../../models').player;

      // Get all transactions for connected users
      const transactions = await PlayerTransaction.findAll({
        where: { buyer_id: { [Op.in]: userIds } },
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'remaining_balance']
          },
          {
            model: Player,
            attributes: ['id', 'name', 'buy_price'],
            include: [
              {
                model: require('../../../models').player_type,
                attributes: ['type']
              }
            ]
          }
        ]
      });

      // Calculate statistics
      const totalTransactions = transactions.length;
      const totalAmountSpent = transactions.reduce((sum, t) => sum + (t.Player?.buy_price || 0), 0);

      // Group by user
      const userStats = {};
      transactions.forEach(transaction => {
        const userId = transaction.buyer_id;
        if (!userStats[userId]) {
          userStats[userId] = {
            user: transaction.User,
            playersCount: 0,
            totalSpent: 0,
            players: []
          };
        }
        userStats[userId].playersCount += 1;
        userStats[userId].totalSpent += transaction.Player?.buy_price || 0;
        userStats[userId].players.push(SocketUtils.modelToPlainObject(transaction.Player));
      });

      return {
        totalUsers: userIds.length,
        totalTransactions,
        totalAmountSpent,
        userStats: Object.values(userStats)
      };
    } catch (error) {
      CONSOLE_LOGGER.error('Error getting final auction stats:', error);
      return {
        totalUsers: 0,
        totalTransactions: 0,
        totalAmountSpent: 0,
        userStats: [],
        error: 'Failed to calculate statistics'
      };
    }
  }

  /**
   * Reset auction system to initial state
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {Object} payload - Reset payload
   */
  static async resetAuction(io, socket, payload) {
    try {
      await UserManager.validateUser(payload, true); // Admin only

      // Reset auction state
      auctionState.reset();

      // Clear database
      const AuctionManager = require('./auctionManager');
      await AuctionManager.clearOlderData();

      await CommunicationManager.broadcastMessage(
        io,
        'auctionReset',
        {
          message: 'Auction system has been reset',
          resetAt: new Date().toISOString()
        }
      );

      CONSOLE_LOGGER.info('Auction system reset successfully');
    } catch (error) {
      CONSOLE_LOGGER.error('resetAuction error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: 'resetAuction',
          text: error.message || 'Failed to reset auction'
        },
        'Error'
      );
    }
  }

  /**
   * Get system health status
   * @param {Object} socket - Socket instance
   */
  static async getSystemHealth(socket) {
    try {
      const auctionStatus = auctionState.getAuctionStatus();
      const registeredUsers = auctionState.getRegisteredUsers();

      const healthData = {
        timestamp: new Date().toISOString(),
        auctionActive: auctionStatus.isAuctionStarted,
        connectedUsers: Object.keys(registeredUsers).length,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
        nodeVersion: process.version
      };

      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: true,
          action: 'systemHealth',
          data: healthData
        },
        'systemHealth'
      );
    } catch (error) {
      CONSOLE_LOGGER.error('getSystemHealth error:', error);
      await CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: false,
          action: 'systemHealth',
          text: error.message || 'Failed to get system health'
        },
        'Error'
      );
    }
  }
}

module.exports = SystemManager;
