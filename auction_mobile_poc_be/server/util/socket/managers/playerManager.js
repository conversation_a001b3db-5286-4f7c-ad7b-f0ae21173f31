const { Op } = require('sequelize');
const Player = require('../../../models').player;
const PlayerType = require('../../../models').player_type;
const Team = require('../../../models').team;
const User = require('../../../models').user;
const PlayerTransaction = require('../../../models').player_transaction;
const AuctionDetails = require('../../../models').auction_details;
const { sequelize } = require('../../../models/index');
const auctionState = require('../state/auctionState');
const SocketUtils = require('../utils/socketUtils');
const CommunicationManager = require('./communicationManager');
const CONSOLE_LOGGER = require('../../../util/logger');

/**
 * Player Manager
 * Handles player-related operations including retrieval, updates, and auction management
 */
class PlayerManager {
  /**
   * Get player by primary key with associations
   * @param {number} id - Player ID
   * @returns {Object|null} Player object or null
   */
  static async getPlayerByPk(id) {
    try {
      return await Player.findByPk(id, {
        include: [
          {
            required: true,
            model: PlayerType
          }
        ]
      });
    } catch (error) {
      CONSOLE_LOGGER.error('Error getting player by PK:', error);
      return null;
    }
  }

  /**
   * Update player status as unsold
   * @param {number} id - Player ID
   * @returns {Array} Update result
   */
  static async updatePlayerAsUnsold(id) {
    console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsUnsold ~ marking player as unsold:', id);
    try {
      const result = await Player.update(
        { status: 'unsold' },
        {
          where: { id },
          returning: true
        }
      );

      console.log(
        '🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsUnsold ~ update result:',
        result[0] > 0 ? 'Success' : 'Failed',
        'Rows affected:',
        result[0]
      );

      // Verify the player was actually updated
      const player = await Player.findByPk(id);
      console.log(
        '🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsUnsold ~ player after update:',
        player ? player.status : 'Player not found'
      );

      return result;
    } catch (error) {
      console.error('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsUnsold ~ error:', error);
      throw error;
    }
  }

  /**
   * Update player as sold and handle transaction
   * @param {number} id - Player ID
   * @param {Object} user - User object
   * @param {number} boughtPrice - Purchase price
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @returns {Array} Update result
   */
  static async updatePlayerAsSold(id, user, boughtPrice, io, socket) {
    console.log(
      '🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ called for player:',
      id, 'user:', user.id, 'price:', boughtPrice
    );
    
    const transaction = await sequelize.transaction();
    let updatedPlayer;
    
    try {
      // Get the latest auction details
      const auctionDetails = await AuctionDetails.findOne({
        order: [['createdAt', 'DESC']]
      });

      const averagePlayerCost = auctionDetails ? auctionDetails.average_player_cost : 0;
      console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ averagePlayerCost:', averagePlayerCost);

      // Get existing player transaction count for this user
      const playerTransactionCount = await PlayerTransaction.count({
        where: { buyer_id: user.id }
      });
      console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ existing playerTransactionCount:', playerTransactionCount);

      const maxBatsman = auctionDetails.max_batsman;
      const maxBowler = auctionDetails.max_bowler;
      const maxWicketkeeper = auctionDetails.max_wicketkeeper;
      const sum = maxBatsman + maxBowler + maxWicketkeeper;

      // Calculate effective transaction count (including current)
      const effectiveTransactionCount = playerTransactionCount + 1;
      console.log(
        '🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ effectiveTransactionCount (including current):',
        effectiveTransactionCount
      );

      // Calculate bonus balance deduction
      const bonusDeduction = user.remaining_balance - averagePlayerCost * (sum - effectiveTransactionCount);
      console.log(
        '🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ bonusDeduction:',
        bonusDeduction
      );

      const finalBonusBalance = bonusDeduction - boughtPrice;
      console.log(
        '🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ finalBonusBalance:',
        finalBonusBalance
      );

      // Validate user object
      if (!user || !user.id) {
        console.error('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ Invalid user object:', user);
        throw new Error('Invalid user object');
      }

      // Get current user values
      const currentUser = await User.findByPk(user.id);
      console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ Current user values:', {
        id: currentUser.id,
        remaining_balance: currentUser.remaining_balance,
        bonus_balance: currentUser.bonus_balance
      });

      // Calculate new values
      const newRemainingBalance = currentUser.remaining_balance - boughtPrice;

      console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ Updating user with:', {
        remaining_balance: newRemainingBalance,
        bonus_balance: finalBonusBalance
      });

      // Update user values
      const updateResult = await User.update(
        {
          remaining_balance: newRemainingBalance,
          bonus_balance: finalBonusBalance
        },
        {
          where: { id: user.id },
          transaction,
          returning: true
        }
      );

      console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ User update result rows affected:', updateResult[0]);

      // Update the player as bought
      updatedPlayer = await Player.update(
        {
          status: 'bought',
          buy_price: boughtPrice
        },
        {
          where: { id },
          returning: true,
          transaction
        }
      );

      // Create player transaction record
      await PlayerTransaction.create(
        {
          player_id: id,
          buyer_id: user.id
        },
        { transaction }
      );

      await transaction.commit();
      console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ Transaction committed successfully');

      // Verify final values
      const finalUser = await User.findByPk(user.id);
      console.log('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ Final user values after commit:', {
        id: finalUser.id,
        remaining_balance: finalUser.remaining_balance,
        bonus_balance: finalUser.bonus_balance
      });
    } catch (e) {
      console.error('🚀🚀🚀 ~ PlayerManager ~ updatePlayerAsSold ~ Error:', e);
      await transaction.rollback();
      throw e;
    }

    await CommunicationManager.getAndFireLiveUsersEvent(io);
    return updatedPlayer;
  }

  /**
   * Get next player for auction based on cycle
   * @param {Object} io - Socket.io instance
   * @param {Object} socket - Socket instance
   * @param {boolean} isRecursive - Whether this is a recursive call
   * @returns {Object|boolean|null} Player object, false if no more players, null if error
   */
  static async getPlayer(io, socket, isRecursive = false) {
    console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ called with isRecursive:', isRecursive);
    
    const cycleStatus = auctionState.getPlayerCycleStatus();
    console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ playerTypesCycleIndex:', cycleStatus.playerTypesCycleIndex);
    console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ playerTypesCycleArr:', cycleStatus.playerTypesCycleArr);

    if (isRecursive) {
      const hasNextRecords = await Player.count({ where: { status: 'not-bought' } });
      console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ hasNextRecords:', hasNextRecords);
      if (!hasNextRecords) {
        return false;
      }
    }

    if (cycleStatus.playerTypesCycleIndex >= cycleStatus.playerTypesCycleArr.length) {
      console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ cycle index exceeded, resetting to 0');
      auctionState.playerTypesCycleIndex = 0;
    }

    const where = { status: 'not-bought' };

    if (cycleStatus.playerIdsAlreadyCycled.length) {
      where.id = { [Op.notIn]: cycleStatus.playerIdsAlreadyCycled };
    }

    console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ searching for player with where:', where);
    console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ player type:', cycleStatus.playerTypesCycleArr[cycleStatus.playerTypesCycleIndex]);

    let player = await Player.findOne({
      where,
      include: [
        {
          required: true,
          model: PlayerType,
          where: { type: cycleStatus.playerTypesCycleArr[cycleStatus.playerTypesCycleIndex] }
        },
        { model: Team }
      ]
    });

    console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ player found:', player ? player.name : 'No player found');

    if (player && player.dataValues && player.dataValues.id) {
      // Add player to already cycled list
      auctionState.addCycledPlayer(player.dataValues.id);
      console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ added player to cycled list:', player.dataValues.id);

      // Clear any existing bids for this player
      const Bidding = require('../../../models').bidding;
      await Bidding.destroy({
        where: { player_id: player.dataValues.id }
      });

      // Set the current auction player ID
      auctionState.setCurrentAuctionPlayer(player.dataValues.id);
      console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ set currentAuctionPlayerId:', player.dataValues.id);

      // Convert player to plain object
      const plainPlayer = SocketUtils.modelToPlainObject(player);

      // Increment cycle index
      auctionState.incrementPlayerCycleIndex();
      return plainPlayer;
    } else {
      console.log(
        '🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ no player found for type:',
        cycleStatus.playerTypesCycleArr[cycleStatus.playerTypesCycleIndex]
      );
      
      auctionState.incrementPlayerCycleIndex();
      console.log('🚀🚀🚀 ~ PlayerManager ~ getPlayer ~ updated playerTypesCycleIndex:', auctionState.getPlayerCycleStatus().playerTypesCycleIndex);

      // Try again with the next player type
      if (!isRecursive) {
        return await PlayerManager.getPlayer(io, socket, true);
      } else {
        return null;
      }
    }
  }

  /**
   * Get upcoming players in auction cycle
   * @returns {Array} Array of upcoming players
   */
  static async getUpcomingPlayers() {
    try {
      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ Attempting to fetch upcoming players');

      const cycleStatus = auctionState.getPlayerCycleStatus();
      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ playerTypesCycleArr:', cycleStatus.playerTypesCycleArr);
      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ playerTypesCycleIndex:', cycleStatus.playerTypesCycleIndex);
      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ playerIdsAlreadyCycled:', cycleStatus.playerIdsAlreadyCycled);

      if (!cycleStatus.playerTypesCycleArr || cycleStatus.playerTypesCycleArr.length === 0) {
        console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ No player types in cycle array');
        return [];
      }

      // Get upcoming player types (excluding the current one)
      let upcomingPlayerTypes = [];
      const cycleCopy = [...cycleStatus.playerTypesCycleArr];

      // Remove the current player type from the cycle
      cycleCopy.splice(cycleStatus.playerTypesCycleIndex, 1);

      // Reorder the array to start from the next index
      if (cycleStatus.playerTypesCycleIndex < cycleCopy.length) {
        upcomingPlayerTypes = [
          ...cycleCopy.slice(cycleStatus.playerTypesCycleIndex),
          ...cycleCopy.slice(0, cycleStatus.playerTypesCycleIndex)
        ];
      } else {
        upcomingPlayerTypes = cycleCopy;
      }

      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ upcomingPlayerTypes:', upcomingPlayerTypes);

      if (upcomingPlayerTypes.length === 0) {
        console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ No upcoming player types');
        return [];
      }

      // Get player types from database
      const playerTypes = await PlayerType.findAll();
      const typeToIdMap = {};
      playerTypes.forEach((pt) => {
        typeToIdMap[pt.type] = pt.id;
      });

      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ typeToIdMap:', typeToIdMap);

      // Get upcoming player type IDs
      const upcomingTypeIds = upcomingPlayerTypes.map((type) => typeToIdMap[type]).filter((id) => id);

      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ upcomingTypeIds:', upcomingTypeIds);

      if (upcomingTypeIds.length === 0) {
        console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ No valid type IDs found');
        return [];
      }

      // Get one player for each upcoming type in the cycle
      const upcomingPlayers = [];
      const addedPlayerIds = new Set();

      // Process each type in order
      for (let i = 0; i < upcomingPlayerTypes.length; i++) {
        const type = upcomingPlayerTypes[i];
        const typeId = typeToIdMap[type];

        if (!typeId) continue;

        // Build the where clause for the query
        const whereClause = {
          status: 'not-bought',
          type_id: typeId
        };

        // Add condition to exclude already cycled players
        if (cycleStatus.playerIdsAlreadyCycled.length > 0) {
          whereClause.id = {
            [Op.notIn]: cycleStatus.playerIdsAlreadyCycled
          };
        }

        // Add condition to exclude already added players
        if (addedPlayerIds.size > 0) {
          if (whereClause.id) {
            whereClause.id = {
              [Op.and]: [whereClause.id, { [Op.notIn]: Array.from(addedPlayerIds) }]
            };
          } else {
            whereClause.id = {
              [Op.notIn]: Array.from(addedPlayerIds)
            };
          }
        }

        console.log(
          `🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ whereClause for type ${type}:`,
          JSON.stringify(whereClause)
        );

        // Find one player of this type
        let player = await Player.findOne({
          where: whereClause,
          include: [
            {
              model: PlayerType,
              attributes: ['id', 'type'],
              as: 'player_type'
            },
            {
              model: Team,
              attributes: ['id', 'name'],
              as: 'team'
            }
          ],
          raw: false,
          nest: true
        });

        // If no player found with exclusion filters, try without playerIdsAlreadyCycled filter
        if (!player && cycleStatus.playerIdsAlreadyCycled.length > 0) {
          console.log(
            `🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ no player found for type ${type} with exclusion filters, trying without playerIdsAlreadyCycled filter`
          );

          const relaxedWhereClause = {
            status: 'not-bought',
            type_id: typeId
          };

          if (addedPlayerIds.size > 0) {
            relaxedWhereClause.id = {
              [Op.notIn]: Array.from(addedPlayerIds)
            };
          }

          player = await Player.findOne({
            where: relaxedWhereClause,
            include: [
              {
                model: PlayerType,
                attributes: ['id', 'type'],
                as: 'player_type'
              },
              {
                model: Team,
                attributes: ['id', 'name'],
                as: 'team'
              }
            ],
            raw: false,
            nest: true
          });
        }

        if (player) {
          // Add this player ID to our tracking set
          addedPlayerIds.add(player.id);

          // Convert to plain object to avoid circular references
          const plainPlayer = {
            id: player.id,
            name: player.name,
            team_id: player.team_id,
            type_id: player.type_id,
            base_price: player.base_price,
            buy_price: player.buy_price,
            status: player.status,
            player_type: player.player_type
              ? {
                  id: player.player_type.id,
                  type: player.player_type.type
                }
              : null,
            team: player.team
              ? {
                  id: player.team.id,
                  name: player.team.name
                }
              : null
          };

          console.log(`🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ found player for type ${type}:`, plainPlayer.name);
          upcomingPlayers.push(plainPlayer);
        } else {
          console.log(`🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ no player found for type ${type}`);
        }
      }

      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayers ~ upcomingPlayers count:', upcomingPlayers.length);
      return upcomingPlayers;
    } catch (error) {
      console.error('Error fetching upcoming players:', error);
      return [];
    }
  }

  /**
   * Get upcoming players for specific player types
   * @param {Array} playerTypes - Array of player type strings
   * @returns {Array} Array of upcoming players for specified types
   */
  static async getUpcomingPlayersForTypes(playerTypes) {
    try {
      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayersForTypes ~ playerTypes:', playerTypes);

      if (!playerTypes || !Array.isArray(playerTypes) || playerTypes.length === 0) {
        console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayersForTypes ~ Invalid or empty playerTypes array');
        return [];
      }

      // Get player type mappings
      const playerTypeRecords = await PlayerType.findAll({
        where: {
          type: { [Op.in]: playerTypes }
        }
      });

      if (playerTypeRecords.length === 0) {
        console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayersForTypes ~ No matching player types found');
        return [];
      }

      const typeIds = playerTypeRecords.map(pt => pt.id);
      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayersForTypes ~ typeIds:', typeIds);

      const cycleStatus = auctionState.getPlayerCycleStatus();

      // Build where clause
      const whereClause = {
        status: 'not-bought',
        type_id: { [Op.in]: typeIds }
      };

      // Exclude already cycled players
      if (cycleStatus.playerIdsAlreadyCycled.length > 0) {
        whereClause.id = {
          [Op.notIn]: cycleStatus.playerIdsAlreadyCycled
        };
      }

      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayersForTypes ~ whereClause:', JSON.stringify(whereClause));

      const players = await Player.findAll({
        where: whereClause,
        include: [
          {
            model: PlayerType,
            attributes: ['id', 'type']
          },
          {
            model: Team,
            attributes: ['id', 'name']
          }
        ],
        limit: 10, // Limit to prevent too many results
        order: [['id', 'ASC']]
      });

      console.log('🚀🚀🚀 ~ PlayerManager ~ getUpcomingPlayersForTypes ~ found players count:', players.length);

      return players.map(player => SocketUtils.modelToPlainObject(player));
    } catch (error) {
      console.error('Error fetching upcoming players for types:', error);
      return [];
    }
  }
}

module.exports = PlayerManager;
