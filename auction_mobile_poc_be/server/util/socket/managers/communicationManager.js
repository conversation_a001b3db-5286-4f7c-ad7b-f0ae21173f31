const _ = require('lodash');
const { Op } = require('sequelize');
const User = require('../../../models').user;
const auctionState = require('../state/auctionState');
const SocketUtils = require('../utils/socketUtils');
const CONSOLE_LOGGER = require('../../../util/logger');

/**
 * Communication Manager
 * Handles all socket communication, broadcasting, and messaging
 */
class CommunicationManager {
  /**
   * Send response to a specific client
   * @param {Object} socket - Socket instance
   * @param {Object} data - Data to send
   * @param {string} event - Event name
   */
  static sendResponseToClient(socket, data, event) {
    try {
      if (socket && typeof socket.emit === 'function') {
        socket.emit(event, data);
        CONSOLE_LOGGER.info(`Response sent to client ${socket.id} for event: ${event}`);
      } else {
        CONSOLE_LOGGER.error('Invalid socket object provided to sendResponseToClient');
      }
    } catch (error) {
      CONSOLE_LOGGER.error('Error sending response to client:', error);
    }
  }

  /**
   * Send response to all admin users
   * @param {string} action - Action name
   * @param {Object} data - Data to send
   * @param {string} text - Message text
   */
  static async sendResponseToAdmins(action, data, text) {
    try {
      const allAdmins = await User.findAll({
        where: { role: 1 },
        attributes: ['id']
      });
      const allAdminIds = allAdmins.map((item) => item.id);

      const registeredUsers = auctionState.getRegisteredUsers;

      // Find admin socket IDs
      const adminSocketIds = [];
      Object.keys(registeredUsers).forEach(socketId => {
        if (allAdminIds.includes(registeredUsers[socketId])) {
          adminSocketIds.push(socketId);
        }
      });

      // Get the global IO instance
      const io = global.IO;
      if (!io) {
        CONSOLE_LOGGER.error('Global IO instance not available');
        return;
      }

      // Send to each admin socket
      CONSOLE_LOGGER.info(`sendResponseToAdmins ~ Found ${adminSocketIds.length} admin sockets: ${JSON.stringify(adminSocketIds)}`);
      CONSOLE_LOGGER.info(`sendResponseToAdmins ~ Sending action: ${action} with data:`, JSON.stringify(data));

      adminSocketIds.forEach(socketId => {
        const targetSocket = Array.from(io.sockets.sockets.values()).find(socket => socket.id === socketId);
        if (targetSocket) {
          const payload = {
            action,
            data,
            text,
            socketId,
            status: true
          };
          targetSocket.emit(action, payload);
          CONSOLE_LOGGER.info(`✅ Sent ${action} to admin socket: ${socketId}`);
          CONSOLE_LOGGER.info(`✅ Payload sent:`, JSON.stringify(payload));
        } else {
          CONSOLE_LOGGER.warn(`❌ Admin socket ${socketId} not found in connected sockets`);
        }
      });

      return { adminSocketIds, data: { action, data, text } };
    } catch (error) {
      CONSOLE_LOGGER.error('Error sending response to admins:', error);

      // Fallback error emission
      const io = global.IO;
      if (io) {
        io.emit('error', {
          status: false,
          action: 'sendResponseToAdmins',
          text: error.message || 'Failed to send response to admins'
        });
      }
      throw error;
    }
  }

  /**
   * Broadcast message to all connected clients
   * @param {Object} io - Socket.io instance
   * @param {string} action - Action name
   * @param {Object} text - Message data
   * @param {Object} successEvent - Success event data (optional)
   * @param {Object} additionalValues - Additional values (optional)
   */
  static async broadcastMessage(io, action, text, successEvent = null, additionalValues = null) {
    console.log('🚀🚀🚀 ~ CommunicationManager ~ broadcastMessage ~ action:', action);
    console.log('🚀🚀🚀 ~ CommunicationManager ~ broadcastMessage ~ clients count:', io.sockets.sockets.size);

    // Update previous state
    auctionState.setPrevState(action, text);

    // Log the message being broadcast for debugging
    if (action === 'Player unsold') {
      console.log('🚀🚀🚀 ~ CommunicationManager ~ broadcastMessage ~ Broadcasting Player unsold event');
      if (text && text.dataValues) {
        console.log('🚀🚀🚀 ~ CommunicationManager ~ broadcastMessage ~ Player ID:', text.dataValues.id);
        console.log('🚀🚀🚀 ~ CommunicationManager ~ broadcastMessage ~ Player Name:', text.dataValues.name);
        console.log('🚀🚀🚀 ~ CommunicationManager ~ broadcastMessage ~ Player Status:', text.dataValues.status);
      }
    }

    const registeredUsers = auctionState.getRegisteredUsers;
    const biddingConfig = auctionState.getBiddingConfig;

    io.sockets.sockets.forEach(async (socket) => {
      try {
        if (successEvent !== null) {
          // Handle success event for specific user
          await CommunicationManager._handleSuccessEvent(socket, successEvent, registeredUsers);
        } else {
          // Handle regular broadcast
          await CommunicationManager._handleRegularBroadcast(
            socket, 
            action, 
            text, 
            additionalValues, 
            registeredUsers, 
            biddingConfig
          );
        }
      } catch (error) {
        CONSOLE_LOGGER.error(`Error broadcasting to socket ${socket.id}:`, error);
      }
    });
  }

  /**
   * Handle success event for specific user
   * @private
   */
  static async _handleSuccessEvent(socket, successEvent, registeredUsers) {
    const prevState = auctionState.getPrevState();
    prevState[0] = 'Bid completed';
    auctionState.setPrevState(prevState[0], prevState[1]);

    const successUserSocketId = _.findKey(registeredUsers, (value) => value === successEvent.user.id);
    
    if (successUserSocketId === socket.id) {
      // Success message for the winning bidder
      CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: true,
          action: 'Success',
          text: {
            message: `Congratulations you have successfully bought ${successEvent.player.name} for ${successEvent.player.buy_price}!`,
            player: successEvent.player.name,
            user: successEvent.user.name,
            price: successEvent.player.buy_price
          }
        },
        'Success'
      );
    } else {
      // Notification for other users
      CommunicationManager.sendResponseToClient(
        socket,
        {
          socketId: socket.id,
          status: true,
          action: 'Sold Player',
          text: {
            message: `${successEvent.user.name} bought ${successEvent.player.name} for ${successEvent.player.buy_price}!`,
            player: successEvent.player.name,
            higghestBidder: successEvent.user.name,
            higghestBid: successEvent.player.buy_price
          }
        },
        'Sold Player'
      );
    }
  }

  /**
   * Handle regular broadcast message
   * @private
   */
  static async _handleRegularBroadcast(socket, action, text, additionalValues, registeredUsers, biddingConfig) {
    const userId = registeredUsers[socket.id];
    
    // Special case: deciding whether next player is biddable for current user
    if (text && text.player && text.player.id && userId) {
      text.text = '';
      
      try {
        const userDetails = await User.findByPk(userId, { 
          attributes: ['remaining_balance'] 
        });
        
        if (userDetails) {
          const config = SocketUtils.getPlayerMaxBidConfig(
            userId, 
            biddingConfig.playersBidStatus, 
            biddingConfig.maxBidConfig
          );
          
          const remainingValue =
            config.BALL.max - config.BALL.current +
            (config.WK.max - config.WK.current) +
            (config.BAT.max - config.BAT.current);
            
          text.isBelowAverage = userDetails.remaining_balance - remainingValue * biddingConfig.averagePlayerCost >= 0;
          
          CONSOLE_LOGGER.info(
            'broadcastMessage :: userDetails.remaining_balance:', userDetails.remaining_balance,
            'remainingValue:', remainingValue,
            'averagePlayerCost:', biddingConfig.averagePlayerCost
          );
          
          if (text.isBelowAverage === false) {
            text.text = "Your remaining balance is below the team's average cost.";
          }
        }

        // Check if user can bid
        if (text.isBelowAverage) {
          text.biddable = !SocketUtils.isQuotaExhaustedForBidding(
            text.player, 
            { id: userId }, 
            biddingConfig.playersBidStatus, 
            biddingConfig.maxBidConfig
          );
        }
        
        CONSOLE_LOGGER.info('broadcastMessage :: text.biddable:', text.biddable);
      } catch (error) {
        CONSOLE_LOGGER.error('Error checking user bidding eligibility:', error);
      }
    }

    // Prepare response object
    let responseObj = {
      action,
      text,
      status: true,
      socketId: socket.id,
      ...SocketUtils.getPlayersConfigResponse(
        auctionState.getPlayerCycleStatus.playerTypesCycleArr,
        biddingConfig.maxBidConfig,
        biddingConfig.averagePlayerCost
      )
    };

    if (additionalValues && Object.keys(additionalValues).length) {
      responseObj = { ...responseObj, ...additionalValues };
    }

    CommunicationManager.sendResponseToClient(socket, responseObj, action);
  }

  /**
   * Get and fire live users event
   * @param {Object} io - Socket.io instance
   */
  static async getAndFireLiveUsersEvent(io) {
    try {
      const connectedUserIds = auctionState.getConnectedUserIds();

      CONSOLE_LOGGER.info(`getAndFireLiveUsersEvent ~ Total connected users: ${connectedUserIds.length}`);
      CONSOLE_LOGGER.info(`getAndFireLiveUsersEvent ~ Connected user IDs: ${JSON.stringify(connectedUserIds)}`);

      // Always query for non-admin users, even if no users are connected
      const records = await User.findAll({
        where: {
          id: { [Op.in]: connectedUserIds.length > 0 ? connectedUserIds : [] },
          role: 2 // Non-admin users
        },
        attributes: ['id', 'name', 'remaining_balance']
      });

      CONSOLE_LOGGER.info(`getAndFireLiveUsersEvent ~ Found ${records.length} non-admin users`);

      // Always send to admins, even if no users are found
      await CommunicationManager.sendResponseToAdmins('liveUsers', { users: records }, 'Live users fetched');

      if (records.length === 0) {
        CONSOLE_LOGGER.info('No non-admin users connected to broadcast');
      } else {
        CONSOLE_LOGGER.info(`Live users event fired for ${records.length} users`);
      }
    } catch (error) {
      CONSOLE_LOGGER.error('Error in getAndFireLiveUsersEvent:', error);
    }
  }

  /**
   * Get and fire live users event
   * @param {Object} io - Socket.io instance
   */
  static async getAndFireLiveUsersEvent(io) {
    try {
      const connectedUserIds = auctionState.getConnectedUserIds();

      CONSOLE_LOGGER.info(`getAndFireLiveUsersEvent ~ Total connected users: ${connectedUserIds.length}`);
      CONSOLE_LOGGER.info(`getAndFireLiveUsersEvent ~ Connected user IDs: ${JSON.stringify(connectedUserIds)}`);

      // Always query for non-admin users, even if no users are connected
      const records = await User.findAll({
        where: {
          id: { [Op.in]: connectedUserIds.length > 0 ? connectedUserIds : [] },
          role: 2 // Non-admin users
        },
        attributes: ['id', 'name', 'remaining_balance']
      });

      CONSOLE_LOGGER.info(`getAndFireLiveUsersEvent ~ Found ${records.length} non-admin users`);

      // Always send to admins, even if no users are found
      await CommunicationManager.sendResponseToAdmins('liveUsers', { users: records }, 'Live users fetched');

      if (records.length === 0) {
        CONSOLE_LOGGER.info('No non-admin users connected to broadcast');
      } else {
        CONSOLE_LOGGER.info(`Live users event fired for ${records.length} users`);
      }
    } catch (error) {
      CONSOLE_LOGGER.error('Error in getAndFireLiveUsersEvent:', error);
    }
  }

  /**
   * Fire minimum player requirement event
   */
  static async fireMinimumPlayerRequirement() {
    try {
      const allAdmins = await User.findAll({ 
        where: { role: 1 }, 
        attributes: ['id'] 
      });
      const allAdminIds = allAdmins.map((item) => item.id);

      const registeredUsers = auctionState.getRegisteredUsers;
      const registeredUserArray = Object.keys(registeredUsers);
      
      // Remove admin from this array
      const filteredRegisteredUserArray = registeredUserArray.filter(
        (socketId) => !allAdminIds.includes(registeredUsers[socketId])
      );

      const payloadToAdmins = {
        isMinimumReached: filteredRegisteredUserArray.length >= auctionState.getMinimumPlayersRequirement()
      };

      await CommunicationManager.sendResponseToAdmins('minimumPlayersRequirement', payloadToAdmins, '');
    } catch (error) {
      CONSOLE_LOGGER.error('Error firing minimum player requirement:', error);
    }
  }

  /**
   * Fire max bid event to specific socket
   * @param {Object} socket - Socket instance
   * @param {Object} data - Event data
   */
  static fireMaxBidEvent(socket, data) {
    CommunicationManager.sendResponseToClient(
      socket,
      {
        data,
        socketId: socket.id,
        status: true,
        action: 'maxBidReached',
        text: 'MAX BID REACHED',
        biddable: false
      },
      'maxBidReached'
    );
  }
}

module.exports = CommunicationManager;
